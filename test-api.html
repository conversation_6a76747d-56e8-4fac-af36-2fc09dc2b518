<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #results {
            margin-top: 20px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>OTA系统 API 连接测试</h1>
    
    <div class="test-section">
        <h2>配置检查</h2>
        <button onclick="checkConfig()">检查配置</button>
        <div id="configResult"></div>
    </div>
    
    <div class="test-section">
        <h2>Gemini API 测试</h2>
        <button onclick="testGeminiAPI()">测试 Gemini API</button>
        <div id="geminiResult"></div>
    </div>
    
    <div class="test-section">
        <h2>GoMyHire API 测试</h2>
        <button onclick="testGoMyHireAPI()">测试 GoMyHire API</button>
        <div id="gomyhireResult"></div>
    </div>
    
    <div id="results"></div>

    <!-- 引入配置文件 -->
    <script src="config.js"></script>
    
    <script>
        // 配置检查
        function checkConfig() {
            const result = document.getElementById('configResult');
            result.innerHTML = '<p>正在检查配置...</p>';
            
            try {
                console.log('SYSTEM_CONFIG:', SYSTEM_CONFIG);
                
                const checks = [
                    {
                        name: 'SYSTEM_CONFIG 存在',
                        pass: typeof SYSTEM_CONFIG !== 'undefined'
                    },
                    {
                        name: 'Gemini API Key 配置',
                        pass: SYSTEM_CONFIG?.API?.GEMINI?.API_KEY && 
                              SYSTEM_CONFIG.API.GEMINI.API_KEY !== 'your-api-key-here' &&
                              SYSTEM_CONFIG.API.GEMINI.API_KEY.startsWith('AIzaSy')
                    },
                    {
                        name: 'Gemini API URL 配置',
                        pass: SYSTEM_CONFIG?.API?.GEMINI?.API_URL?.includes('generativelanguage.googleapis.com')
                    },
                    {
                        name: 'GoMyHire API URL 配置',
                        pass: SYSTEM_CONFIG?.API?.BASE_URL?.includes('gomyhire.com')
                    }
                ];
                
                let html = '<h3>配置检查结果:</h3><ul>';
                let allPassed = true;
                
                checks.forEach(check => {
                    const status = check.pass ? '✅' : '❌';
                    html += `<li>${status} ${check.name}</li>`;
                    if (!check.pass) allPassed = false;
                });
                
                html += '</ul>';
                
                if (allPassed) {
                    html += '<p style="color: green;">✅ 所有配置检查通过！</p>';
                    result.className = 'test-section success';
                } else {
                    html += '<p style="color: red;">❌ 部分配置检查失败！</p>';
                    result.className = 'test-section error';
                }
                
                // 显示实际配置值（隐藏敏感信息）
                html += '<h4>当前配置:</h4>';
                html += `<p><strong>Gemini API Key:</strong> ${SYSTEM_CONFIG?.API?.GEMINI?.API_KEY?.substring(0, 10)}...（${SYSTEM_CONFIG?.API?.GEMINI?.API_KEY?.length} 字符）</p>`;
                html += `<p><strong>Gemini API URL:</strong> ${SYSTEM_CONFIG?.API?.GEMINI?.API_URL}</p>`;
                html += `<p><strong>GoMyHire API URL:</strong> ${SYSTEM_CONFIG?.API?.BASE_URL}</p>`;
                
                result.innerHTML = html;
                
            } catch (error) {
                result.innerHTML = `<p style="color: red;">❌ 配置检查失败: ${error.message}</p>`;
                result.className = 'test-section error';
            }
        }
        
        // 测试 Gemini API
        async function testGeminiAPI() {
            const result = document.getElementById('geminiResult');
            result.innerHTML = '<p>正在测试 Gemini API...</p>';
            
            try {
                const apiKey = SYSTEM_CONFIG?.API?.GEMINI?.API_KEY;
                const apiUrl = SYSTEM_CONFIG?.API?.GEMINI?.API_URL;
                
                if (!apiKey || !apiUrl) {
                    throw new Error('Gemini API 配置不完整');
                }
                
                const response = await fetch(`${apiUrl}?key=${apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: 'Hello, this is a test message. Please respond with "API test successful".'
                            }]
                        }],
                        generationConfig: {
                            temperature: 0.7,
                            topK: 40,
                            topP: 0.95,
                            maxOutputTokens: 100
                        }
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.candidates && data.candidates[0]) {
                    result.innerHTML = `
                        <h4>✅ Gemini API 测试成功！</h4>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>响应:</strong> ${data.candidates[0].content.parts[0].text}</p>
                    `;
                    result.className = 'test-section success';
                } else {
                    throw new Error(`API 响应错误: ${response.status} - ${JSON.stringify(data)}`);
                }
                
            } catch (error) {
                result.innerHTML = `
                    <h4>❌ Gemini API 测试失败</h4>
                    <p><strong>错误:</strong> ${error.message}</p>
                    <p><strong>建议:</strong> 请检查 API Key 是否正确，是否有足够的配额</p>
                `;
                result.className = 'test-section error';
                console.error('Gemini API 测试失败:', error);
            }
        }
        
        // 测试 GoMyHire API
        async function testGoMyHireAPI() {
            const result = document.getElementById('gomyhireResult');
            result.innerHTML = '<p>正在测试 GoMyHire API...</p>';
            
            try {
                const apiUrl = SYSTEM_CONFIG?.API?.BASE_URL;
                
                if (!apiUrl) {
                    throw new Error('GoMyHire API URL 配置不完整');
                }
                
                // 测试 API 连通性（不需要认证的端点）
                const response = await fetch(`${apiUrl}/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    result.innerHTML = `
                        <h4>✅ GoMyHire API 连接成功！</h4>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>API URL:</strong> ${apiUrl}</p>
                    `;
                    result.className = 'test-section success';
                } else {
                    // 404 或其他错误可能是正常的，因为可能没有 /health 端点
                    result.innerHTML = `
                        <h4>⚠️ GoMyHire API 状态</h4>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>说明:</strong> API 可达，但端点不存在（这是正常的）</p>
                        <p><strong>API URL:</strong> ${apiUrl}</p>
                    `;
                    result.className = 'test-section';
                }
                
            } catch (error) {
                result.innerHTML = `
                    <h4>❌ GoMyHire API 测试失败</h4>
                    <p><strong>错误:</strong> ${error.message}</p>
                    <p><strong>建议:</strong> 请检查网络连接和 API URL 配置</p>
                `;
                result.className = 'test-section error';
                console.error('GoMyHire API 测试失败:', error);
            }
        }
        
        // 页面加载完成后自动检查配置
        window.addEventListener('load', () => {
            checkConfig();
        });
    </script>
</body>
</html>
