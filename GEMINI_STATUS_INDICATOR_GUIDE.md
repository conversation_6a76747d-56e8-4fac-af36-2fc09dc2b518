# Gemini 状态指示器功能说明

## 📋 功能概述

新增的 Gemini 状态指示器位于系统头部，用于实时显示 Gemini AI 服务的连接状态，帮助用户了解 AI 功能的可用性。

## 🎯 功能特性

### 1. 状态显示
- **检测中（黄色）**: 正在检测 Gemini API 连接状态
- **已连接（绿色）**: Gemini API 连接正常，AI 功能可用
- **连接失败（红色）**: Gemini API 连接失败，AI 功能不可用

### 2. 交互功能
- **点击检测**: 点击状态指示器可手动触发连接状态检测
- **自动检测**: 系统每5分钟自动检测一次连接状态
- **初始检测**: 应用启动时自动进行首次连接检测

### 3. 视觉效果
- **动画效果**: 检测中状态有脉冲动画效果
- **悬停效果**: 鼠标悬停时有轻微的阴影和位移效果
- **响应式设计**: 在移动设备上自动调整布局

## 🔧 技术实现

### HTML 结构
```html
<div class="gemini-status-indicator" id="geminiStatusIndicator" title="点击检测 Gemini API 连接状态">
    <div class="status-light" id="geminiStatusLight"></div>
    <span class="status-text" id="geminiStatusText">检测中...</span>
</div>
```

### CSS 样式类
- `.gemini-status-indicator`: 主容器样式
- `.status-light`: 状态指示灯样式
- `.status-light.connected`: 连接成功状态（绿色）
- `.status-light.disconnected`: 连接失败状态（红色）
- `.status-light.checking`: 检测中状态（黄色，带动画）

### JavaScript 方法
- `initializeGeminiStatusIndicator()`: 初始化状态指示器
- `checkGeminiConnection()`: 检测 Gemini 连接状态
- `updateGeminiStatusIndicator()`: 更新状态指示器显示
- `GeminiService.checkConnection()`: Gemini 服务连接检测

## 📱 使用说明

### 1. 查看状态
- 登录系统后，在页面头部可以看到 Gemini 状态指示器
- 指示器显示当前的连接状态和状态文字

### 2. 手动检测
- 点击状态指示器可以手动触发连接检测
- 检测过程中指示器会显示"检测中..."状态

### 3. 状态含义
- **绿色 + "Gemini 已连接"**: AI 功能正常，可以使用订单处理功能
- **红色 + "Gemini 连接失败"**: AI 功能不可用，请检查网络或 API 配置
- **黄色 + "检测中..."**: 正在检测连接状态，请稍候

## 🛠️ 故障排除

### 连接失败的可能原因
1. **API Key 未配置**: 检查 `config.js` 中的 `GEMINI_API_KEY` 配置
2. **API Key 无效**: 验证 API Key 是否正确且未过期
3. **网络问题**: 检查网络连接是否正常
4. **API 配额用完**: 检查 Gemini API 的使用配额
5. **API 服务异常**: Gemini 服务可能暂时不可用

### 调试方法
1. 打开浏览器开发者工具查看控制台日志
2. 查看网络请求是否成功发送
3. 检查 API 响应状态码和错误信息
4. 使用 `test-gemini-api.html` 进行独立测试

## 📊 日志记录

系统会记录以下 Gemini 相关日志：
- 连接状态检测开始和结果
- API 请求成功和失败信息
- 状态指示器更新操作
- 错误和异常信息

## 🔄 自动检测机制

- **初始检测**: 应用启动时立即检测
- **定期检测**: 每5分钟自动检测一次
- **手动检测**: 用户点击时立即检测
- **超时设置**: 单次检测超时时间为10秒

## 📈 性能优化

- 使用轻量级测试请求减少 API 消耗
- 合理的检测间隔避免频繁请求
- 缓存连接状态减少重复检测
- 异步检测不阻塞用户界面

## 🎨 样式定制

可以通过修改 CSS 变量来定制指示器样式：
- 指示灯颜色：修改 `.status-light` 相关类的 `background` 属性
- 动画效果：调整 `@keyframes pulse` 动画参数
- 尺寸大小：修改 `.status-light` 的 `width` 和 `height`
- 位置布局：调整 `.gemini-status-indicator` 的布局属性

## 🔮 未来扩展

计划中的功能增强：
1. 显示 API 响应时间
2. 显示 API 使用配额信息
3. 连接历史记录
4. 更详细的错误信息提示
5. 多种 AI 服务状态监控
