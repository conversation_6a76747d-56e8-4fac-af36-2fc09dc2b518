<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            paddi                // 测试简单请求
                const testUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`;: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #e2e3e5;
            color: #383d41;
            border: 1px solid #d6d8db;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Gemini API 测试工具</h1>
        
        <div class="test-section">
            <h3>📋 配置检查</h3>
            <button onclick="checkConfig()">检查配置</button>
            <div id="config-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔬 API 连接测试</h3>
            <button onclick="testSimpleRequest()">测试简单请求</button>
            <button onclick="testOrderProcessing()">测试订单处理</button>
            <div id="api-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🛠️ 错误诊断</h3>
            <button onclick="diagnoseApiKey()">API Key 诊断</button>
            <button onclick="testQuota()">配额检查</button>
            <div id="diagnosis-result" class="result"></div>
        </div>
    </div>

    <!-- 引入配置文件 -->
    <script src="config.js"></script>
    <script>
        // 检查配置
        function checkConfig() {
            const resultDiv = document.getElementById('config-result');
            
            try {
                const checks = [
                    {
                        name: '系统配置存在',
                        pass: typeof SYSTEM_CONFIG !== 'undefined',
                        value: typeof SYSTEM_CONFIG !== 'undefined' ? '✓' : '✗'
                    },
                    {
                        name: 'Gemini API Key',
                        pass: SYSTEM_CONFIG?.API?.GEMINI?.API_KEY && 
                              SYSTEM_CONFIG.API.GEMINI.API_KEY.startsWith('AIzaSy'),
                        value: SYSTEM_CONFIG?.API?.GEMINI?.API_KEY ? 
                               SYSTEM_CONFIG.API.GEMINI.API_KEY.substr(0, 10) + '...' : '未设置'
                    },
                    {
                        name: 'Gemini API URL',
                        pass: SYSTEM_CONFIG?.API?.GEMINI?.API_URL?.includes('generativelanguage.googleapis.com'),
                        value: SYSTEM_CONFIG?.API?.GEMINI?.API_URL || '未设置'
                    },
                    {
                        name: 'API 版本',
                        pass: SYSTEM_CONFIG?.API?.GEMINI?.API_URL?.includes('/v1/'),
                        value: SYSTEM_CONFIG?.API?.GEMINI?.API_URL?.includes('/v1/') ? 'v1 (正确)' : 
                               SYSTEM_CONFIG?.API?.GEMINI?.API_URL?.includes('/v1beta/') ? 'v1beta (需要更新)' : '未知'
                    },
                    {
                        name: '模型版本',
                        pass: SYSTEM_CONFIG?.API?.GEMINI?.API_URL?.includes('gemini-2.0'),
                        value: SYSTEM_CONFIG?.API?.GEMINI?.API_URL?.includes('gemini-2.0') ? 'Gemini 2.0 (最新)' : 
                               SYSTEM_CONFIG?.API?.GEMINI?.API_URL?.includes('gemini-1.5') ? 'Gemini 1.5 (可用)' : 
                               SYSTEM_CONFIG?.API?.GEMINI?.API_URL?.includes('gemini-pro') ? 'Gemini Pro (旧版)' : '未知'
                    }
                ];

                let output = '配置检查结果：\n\n';
                let allPassed = true;

                checks.forEach(check => {
                    const status = check.pass ? '✅' : '❌';
                    output += `${status} ${check.name}: ${check.value}\n`;
                    if (!check.pass) allPassed = false;
                });

                output += `\n总体状态: ${allPassed ? '✅ 配置正常' : '❌ 需要修复配置'}`;

                resultDiv.className = 'result ' + (allPassed ? 'success' : 'error');
                resultDiv.textContent = output;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '配置检查失败: ' + error.message;
            }
        }

        // 测试简单请求
        async function testSimpleRequest() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试 API 连接...';

            try {
                const apiKey = SYSTEM_CONFIG?.API?.GEMINI?.API_KEY;
                const apiUrl = SYSTEM_CONFIG?.API?.GEMINI?.API_URL;

                if (!apiKey || !apiUrl) {
                    throw new Error('API Key 或 URL 未配置');
                }

                const response = await fetch(`${apiUrl}?key=${apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: '请回复"连接成功"来确认API工作正常。'
                            }]
                        }],
                        generationConfig: {
                            temperature: 0.1,
                            maxOutputTokens: 100
                        }
                    })
                });

                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('错误响应:', errorText);
                    
                    let errorMessage;
                    try {
                        const errorJson = JSON.parse(errorText);
                        errorMessage = errorJson.error?.message || errorText;
                    } catch {
                        errorMessage = errorText;
                    }
                    
                    throw new Error(`HTTP ${response.status}: ${errorMessage}`);
                }

                const data = await response.json();
                console.log('响应数据:', data);

                if (data.candidates && data.candidates[0]) {
                    const responseText = data.candidates[0].content.parts[0].text;
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ API 连接成功！\n\n响应: ${responseText}\n\n完整响应数据:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    throw new Error('API 响应格式异常: ' + JSON.stringify(data));
                }

            } catch (error) {
                console.error('API 测试失败:', error);
                
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ API 连接失败\n\n错误信息: ${error.message}\n\n可能的原因:\n- API Key 无效或已过期\n- API 配额已用完\n- 网络连接问题\n- API URL 不正确`;
            }
        }

        // 测试订单处理
        async function testOrderProcessing() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试订单处理功能...';

            try {
                const apiKey = SYSTEM_CONFIG?.API?.GEMINI?.API_KEY;
                const apiUrl = SYSTEM_CONFIG?.API?.GEMINI?.API_URL;

                const testOrder = `
OTA：CHONG
日期：2025-06-03
时间：14:30
客人：张三
电话：+60123456789
航班：MH123
从：吉隆坡国际机场KLIA
到：Hilton Kuala Lumpur
备注：举牌接机
                `;

                const prompt = `请分析以下订单信息并提取关键数据：

${testOrder}

请以JSON格式返回提取的信息：
{
  "customer_name": "客人姓名",
  "phone": "电话号码",
  "date": "日期",
  "time": "时间",
  "flight_info": "航班信息",
  "pickup": "接机地点",
  "destination": "目的地",
  "service_type": "服务类型",
  "notes": "备注"
}`;

                const response = await fetch(`${apiUrl}?key=${apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: prompt
                            }]
                        }],
                        generationConfig: {
                            temperature: 0.1,
                            maxOutputTokens: 1024
                        }
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                const responseText = data.candidates[0].content.parts[0].text;

                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 订单处理测试成功！\n\nAI 响应:\n${responseText}`;

            } catch (error) {
                console.error('订单处理测试失败:', error);
                
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 订单处理测试失败\n\n错误信息: ${error.message}`;
            }
        }

        // 诊断 API Key
        async function diagnoseApiKey() {
            const resultDiv = document.getElementById('diagnosis-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在诊断 API Key...';

            try {
                const apiKey = SYSTEM_CONFIG?.API?.GEMINI?.API_KEY;
                
                if (!apiKey) {
                    throw new Error('API Key 未设置');
                }

                let diagnosis = 'API Key 诊断结果:\n\n';
                
                // 检查格式
                if (apiKey.startsWith('AIzaSy')) {
                    diagnosis += '✅ API Key 格式正确\n';
                } else {
                    diagnosis += '❌ API Key 格式不正确\n';
                }

                // 检查长度
                if (apiKey.length === 39) {
                    diagnosis += '✅ API Key 长度正确\n';
                } else {
                    diagnosis += `❌ API Key 长度不正确 (${apiKey.length}, 应为39)\n`;
                }

                // 测试简单请求
                const testUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`;
                
                const response = await fetch(testUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: 'test'
                            }]
                        }]
                    })
                });

                if (response.ok) {
                    diagnosis += '✅ API Key 验证成功\n';
                } else {
                    const errorText = await response.text();
                    diagnosis += `❌ API Key 验证失败: ${response.status}\n`;
                    diagnosis += `错误详情: ${errorText}\n`;
                }

                resultDiv.className = 'result success';
                resultDiv.textContent = diagnosis;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `诊断失败: ${error.message}`;
            }
        }

        // 测试配额
        async function testQuota() {
            const resultDiv = document.getElementById('diagnosis-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在检查 API 配额...';

            try {
                const apiKey = SYSTEM_CONFIG?.API?.GEMINI?.API_KEY;
                const apiUrl = SYSTEM_CONFIG?.API?.GEMINI?.API_URL;

                // 发送几个请求测试配额
                const promises = [];
                for (let i = 0; i < 3; i++) {
                    promises.push(
                        fetch(`${apiUrl}?key=${apiKey}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                contents: [{
                                    parts: [{
                                        text: `测试请求 ${i + 1}`
                                    }]
                                }]
                            })
                        })
                    );
                }

                const results = await Promise.all(promises);
                
                let successCount = 0;
                let errorCount = 0;
                let quotaError = false;

                for (const response of results) {
                    if (response.ok) {
                        successCount++;
                    } else {
                        errorCount++;
                        if (response.status === 429) {
                            quotaError = true;
                        }
                    }
                }

                let output = `配额测试结果:\n\n`;
                output += `成功请求: ${successCount}/3\n`;
                output += `失败请求: ${errorCount}/3\n`;

                if (quotaError) {
                    output += `\n❌ 检测到配额限制 (HTTP 429)\n`;
                    output += `建议: 等待一段时间后重试，或升级API配额\n`;
                } else if (successCount === 3) {
                    output += `\n✅ API 配额正常\n`;
                } else {
                    output += `\n⚠️ 部分请求失败，但不是配额问题\n`;
                }

                resultDiv.className = 'result ' + (quotaError ? 'error' : successCount === 3 ? 'success' : 'info');
                resultDiv.textContent = output;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `配额检查失败: ${error.message}`;
            }
        }

        // 页面加载时自动检查配置
        window.addEventListener('load', function() {
            setTimeout(checkConfig, 500);
        });
    </script>
</body>
</html>
